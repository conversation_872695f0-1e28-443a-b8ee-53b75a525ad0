package com.easylive.back.payment.pub;

import com.easylive.back.payment.pub.model.MyModel;
import feign.Param;
import feign.RequestLine;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2019-07-17
 */
@FeignClient(name = "payment")

public interface MyService {
    @RequestLine("GET /test/uid?uid={uid}")
    @GetMapping("/test/uid")
    MyModel getById(@RequestParam("uid") @Param("uid") Long uid);
}
