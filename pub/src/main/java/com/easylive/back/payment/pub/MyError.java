package com.easylive.back.payment.pub;

import com.easylive.rpc.http.ResponseError;
import org.springframework.http.HttpStatus;

/**
 * 本服务业务错误码
 */
public enum MyError implements ResponseError {


    /**
     * 业务错误码，从一个范围开始，每个范围1000个错误码，例如UserService，10000-10999
     */
    E_MY_BIZ_ERROR(90001);


    private final int code;
    private HttpStatus status;

    MyError(final int code) {
        this.code = code;
        status = HttpStatus.UNPROCESSABLE_ENTITY;
    }

    MyError(final int value, HttpStatus status) {
        this.code = value;
        this.status = status;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public HttpStatus getHttpStatus() {
        return status;
    }

    @Override
    public String getName() {
        return null;
    }

    @Override
    public String getMessage() {
        return null;
    }

    @Override
    public boolean isOverrideMessage() {
        return false;
    }
}
