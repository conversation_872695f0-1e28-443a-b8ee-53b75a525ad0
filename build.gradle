version '1.0.0-SNAPSHOT'



apply plugin: 'eclipse-wtp'
apply plugin: 'org.springframework.boot'
apply plugin: 'io.spring.dependency-management'
apply plugin: 'war'


buildscript {
    ext {
        springBootVersion = '2.2.9.RELEASE'
    }
    repositories {
        maven { url = 'https://maven.aliyun.com/repository/central/' }
        mavenCentral()
    }
    dependencies {
        classpath "org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}"
    }
}


allprojects {
    apply plugin: 'java'
    apply plugin: 'maven-publish'
    apply plugin: 'idea'

    group 'com.easylive.mgs'

    sourceCompatibility = JavaVersion.VERSION_11
    repositories {
        maven { url = 'https://maven.aliyun.com/repository/central/' }
        mavenCentral()
        maven { url = 'https://maven.scmagic.net/repository/maven-public/' }
    }

    dependencies {
        compile "com.easylive:common-event:1.3.1"
        compile "com.easylive:common-rpc:1.2.8"
        compile "com.easylive:common-util:1.2.16"
    }

    tasks.withType(JavaCompile) {
        options.compilerArgs << "-Xlint:deprecation"
    }

    tasks.withType(Javadoc).all { enabled = false }

}




dependencies {

    compile project(":pub")
    compile 'com.sun.mail:javax.mail:1.6.2'
    compile 'org.springframework.cloud:spring-cloud-starter-openfeign:2.2.9.RELEASE'
    compile 'com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery:2.2.7.RELEASE'

    compile "org.apache.commons:commons-pool2:2.8.1"
    compile ("org.springframework.boot:spring-boot-starter-web:${springBootVersion}") {
        exclude module: "spring-boot-starter-tomcat"
    }
    compile "org.springframework.boot:spring-boot-starter-validation:${springBootVersion}"
    compile "org.springframework.boot:spring-boot-starter-data-jpa:${springBootVersion}"
    compile "org.springframework.boot:spring-boot-starter:${springBootVersion}"
    compile "org.springframework.boot:spring-boot-starter-data-redis:${springBootVersion}"
    compile "mysql:mysql-connector-java:8.0.21"
    compile "io.springfox:springfox-swagger2:2.9.2"
    compile "io.springfox:springfox-swagger-ui:2.9.2"

    compile ("org.apache.zookeeper:zookeeper:3.6.2") {
        exclude group: 'log4j', module: 'log4j'
        exclude group: 'org.slf4j', module: 'slf4j-log4j12'
    }
    compile "org.apache.curator:curator-recipes:5.1.0"

    compileOnly 'org.projectlombok:lombok:1.18.12'
    annotationProcessor 'org.projectlombok:lombok:1.18.12'


    compile "com.easylive.mgs:user:*******-SNAPSHOT"



    compile "org.springframework.boot:spring-boot-starter-undertow:${springBootVersion}"

    // 服务监控
    compile group: 'io.micrometer', name: 'micrometer-registry-prometheus', version: '1.0.6'
    compile "org.springframework.boot:spring-boot-starter-actuator:${springBootVersion}"

    testCompile "org.springframework.boot:spring-boot-starter-test:${springBootVersion}"
    testCompile 'com.h2database:h2:1.4.200'
    testCompile 'org.projectlombok:lombok:1.18.12'
    testAnnotationProcessor 'org.projectlombok:lombok:1.18.12'
}

task createProperties(dependsOn: processResources) {
    doLast {
        def stdout = new ByteArrayOutputStream()
        exec {
            commandLine 'git', 'rev-parse', '--short', 'HEAD'
            standardOutput = stdout
        }
        def commit = stdout.toString().trim()

        new File("$buildDir/resources/main/version.properties").withWriter { w ->
            Properties p = new Properties()
            p['version'] = project.version.toString()
            p['commit'] = commit
            p.store w, null
        }
    }
}

classes {
    dependsOn createProperties
}

