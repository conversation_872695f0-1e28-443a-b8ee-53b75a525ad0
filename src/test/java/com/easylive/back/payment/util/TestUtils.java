package com.easylive.back.payment.util;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class TestUtils {
    private static Logger logger = LoggerFactory.getLogger(TestUtils.class);


    @Test
    public void testBigDecimal() {
        BigDecimal a = new BigDecimal("1000.055000");
        logger.info(String.format("%s", a));
        logger.info(String.format("%s", a.stripTrailingZeros().toPlainString()));
        logger.info(String.format("%s", a.setScale(4, RoundingMode.HALF_UP)));
        logger.info(String.format("%s", a.setScale(2, RoundingMode.HALF_UP)));
        logger.info(String.format("%s", a.setScale(0, RoundingMode.HALF_UP).stripTrailingZeros()));
        logger.info(String.format("%s", a.setScale(0, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString()));

        logger.info(a.add(new BigDecimal(1000)).toPlainString());
        logger.info(a.toPlainString());

    }


    @Test
    public void testRandom() {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz1234567890~!@#^&*()_+-=[]{},<>";
        logger.info(RandomStringUtils.random(64, chars));
    }


}
