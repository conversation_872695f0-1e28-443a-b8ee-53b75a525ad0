package com.easylive.back.payment.controller;

import com.easylive.back.payment.common.BaseControllerTest;
import com.easylive.back.payment.pub.MyError;
import com.easylive.back.payment.pub.MyService;
import com.easylive.back.payment.pub.model.MyModel;
import com.easylive.rpc.http.BizException;
import com.easylive.rpc.http.feign.ServiceFactory;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2019-06-06
 */


public class TestExampleController extends BaseControllerTest {

    @Test
    public void testWithSpring() throws Exception {
        commonTestGet("/test/uid?uid=1", actions ->
                actions.andExpect(commonJsonMapContains("name", "12345678"))
                        .andExpect(commonJsonMapContains("uid", 1))
        );

        commonTestGetBizError("/test/uid?uid=0", actions ->
                actions.andExpect(jsonPath(BASE_NODE_CODE, jsonValueIs(MyError.E_MY_BIZ_ERROR.getCode())))
        );
    }


    @Test
    public void testWithMGS() {
        MyService myService = ServiceFactory.create(MyService.class, serviceUrl);
        MyModel model = myService.getById(1L);
        Assert.assertEquals(model.getUid(), 1L);

        try {
            myService.getById(0L);
        } catch (BizException e) {
            Assert.assertEquals(e.getCode(), MyError.E_MY_BIZ_ERROR.getCode());
        }
    }

}
