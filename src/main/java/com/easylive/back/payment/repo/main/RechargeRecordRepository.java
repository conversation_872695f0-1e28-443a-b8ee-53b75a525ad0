package com.easylive.back.payment.repo.main;

import com.easylive.back.payment.entity.RechargeRecordEntity;
import com.easylive.back.payment.entity.RechargeStatProjection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.Date;

@Repository
public interface RechargeRecordRepository extends JpaRepository<RechargeRecordEntity, Long> {
    RechargeRecordEntity findByOrderId(String orderId);

    @Modifying
    @Transactional
    @Query("update RechargeRecordEntity set status = 0, confirmStatus = 2 where completeTime >= ?1 and completeTime < ?2 and status = 5")
    int setStatusOff(Date start, Date end);

    @Modifying
    @Transactional
    @Query("update RechargeRecordEntity set status = 5, confirmStatus = 0 where completeTime >= ?1 and completeTime < ?2 and status = 0 and confirmStatus = 2")
    int setStatusOn(Date start, Date end);

    @Query("select sum(rmb) as amount, count(*) as cnt from RechargeRecordEntity where completeTime >= ?1 and completeTime < ?2 and status = 5")
    RechargeStatProjection statOfOn(Date start, Date end);

    @Query("select sum(rmb) as amount, count(*) as cnt from RechargeRecordEntity where completeTime >= ?1 and completeTime < ?2 and status = 0 and confirmStatus = 2")
    RechargeStatProjection statOfOff(Date start, Date end);
}
