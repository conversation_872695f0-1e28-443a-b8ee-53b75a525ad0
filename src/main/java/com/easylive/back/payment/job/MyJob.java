package com.easylive.back.payment.job;

import com.easylive.back.payment.service.PaymentMonitorService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;


@Service
@Slf4j
public class MyJob {

    @Autowired
    private PaymentMonitorService paymentMonitorService;

    @PostConstruct
    private void init() {

    }


    @Scheduled(cron = "0 0 0 * * *")
    private void cronJob() {
        paymentMonitorService.todaySummary();
    }

    @Scheduled(fixedDelay = 5000, initialDelay = 3000)
    private void job2() {

    }

}
