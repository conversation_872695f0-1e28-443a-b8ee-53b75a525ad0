package com.easylive.back.payment.service;

import com.easylive.back.payment.entity.RechargeRecordEntity;
import com.easylive.back.payment.entity.RechargeStatProjection;
import com.easylive.back.payment.enums.RechargeEventType;
import com.easylive.back.payment.model.RechargeEvent;
import com.easylive.back.payment.repo.main.RechargeRecordRepository;
import com.easylive.back.payment.util.EmailSender;
import com.easylive.common.event.EventConsumer;
import com.easylive.common.event.EventItem;
import com.easylive.common.util.SystemUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.transaction.Transactional;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicLong;
import org.springframework.scheduling.TaskScheduler;

/**
 * <AUTHOR>
 * @date 2025/2/7
 */
@Service
@Slf4j
public class PaymentMonitorService {

    @Autowired
    private RechargeRecordRepository rechargeRecordRepository;

    @Autowired(required = false)
    private EventConsumer eventConsumer;

    private static final Set<String> mids = Set.of("2021004126642033", "2021004109631024", "2021004132629023", "2018073160761807");

    public MerchantConfig setMerchantConfig(String mid, Long amountLimit, String startTime, String endTime) {
        MerchantConfig config = merchantConfigs.get(mid);
        if( config != null ) {
            if( amountLimit != null)
                config.setAmountLimit(amountLimit);
            if( startTime != null ) {
                Date startTimeDt = getDateFromTime(startTime);
                if ( startTimeDt != null ) {
                    config.setStartTimeDt(startTimeDt);
                    config.setStartTime(startTime);
                }
            }
            if( endTime != null ) {
                Date endTimeDt = getDateFromTime(endTime);
                if ( endTimeDt != null ) {
                    config.setEndTimeDt(endTimeDt);
                    config.setEndTime(endTime);
                }
            }
        }
        return config;
    }

    public RechargeStatProjection statRecord(boolean on, Date startDt, Date endDt) {
        if( on ) {
            return rechargeRecordRepository.statOfOn(startDt, endDt);
        } else {
            return rechargeRecordRepository.statOfOff(startDt, endDt);
        }
    }
    @Transactional
    public int operateRecord(boolean on, Date startDt, Date endDt) {
        if( on ) {
            return rechargeRecordRepository.setStatusOn(startDt, endDt);
        } else {
            return rechargeRecordRepository.setStatusOff(startDt, endDt);
        }
    }

    //private static Set<String> mids = Set.of("2021004196669836");

    @Data
    public static class MerchantConfig {
        private String mid;
        private String startTime;
        private String endTime;
        @JsonFormat(shape = JsonFormat.Shape.STRING, timezone = "GMT+8")
        private Date startTimeDt;
        @JsonFormat(shape = JsonFormat.Shape.STRING, timezone = "GMT+8")
        private Date endTimeDt;
        private long amountLimit = 200000;
        private long amount;

        public MerchantConfig(String mid) {
            this.mid = mid;
        }

        public MerchantConfig(String mid, String startTime, String endTime, long amountLimit) {
            this.mid = mid;
            this.startTime = startTime;
            this.endTime = endTime;
            this.amountLimit = amountLimit;
            this.startTimeDt = getDateFromTime(startTime);
            this.endTimeDt = getDateFromTime(endTime);
        }
    }

    private static final Map<String, MerchantConfig> merchantConfigs = new HashMap<>();

    static {
        for (String mid : mids) {
            merchantConfigs.put(mid, new MerchantConfig(mid));
        }
        MerchantConfig config = new MerchantConfig("202312221109", "02:00", "06:00", 0);
        merchantConfigs.put(config.getMid(), config);

        MerchantConfig config2 = new MerchantConfig("202409021500", "02:00", "06:00", 0);
        merchantConfigs.put(config2.getMid(), config2);
    }

    @Autowired
    @Qualifier("paymentThreadPool")
    private Executor taskExecutor;

    @Autowired
    private TaskScheduler taskScheduler;

    private final AtomicLong totalProcessed = new AtomicLong(0);
    private final AtomicLong totalAmount = new AtomicLong(0);
    private Date today = new Date();

    @PostConstruct
    public void init() {
        log.info("Register Recharge Event");
        eventConsumer.addTopic(RechargeEventType.TOPIC_NAME)
                .register(RechargeEventType.COMPLETE.getValue(), RechargeEvent.class, this::onRechargeEvent);
        eventConsumer.start(taskExecutor);
    }


    public String getConfigs() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.enable(SerializationFeature.INDENT_OUTPUT);
        try {
            return objectMapper.writeValueAsString(merchantConfigs);
        } catch (Exception e) {
            log.error("Failed to convert merchantConfigs to JSON", e);
            return "{}";
        }
    }

    private void onRechargeEvent(EventItem<RechargeEvent> eventData) {
        log.info("Recharge event received: {}", eventData.getData());
        RechargeEvent rechargeEvent = eventData.getData();
        String mid = rechargeEvent.getMerchantId();
        MerchantConfig config = merchantConfigs.get(mid);
        Date now = new Date();
        if (config != null) {
            log.info("{},{}, {}, {}, {}", config.getAmount(),config.getAmountLimit(), now, config.getStartTimeDt(), config.getEndTimeDt());
            if (config.getAmount() >= config.getAmountLimit())
                return;

            if (config.getStartTimeDt() != null && now.before(config.getStartTimeDt())) {
                return;
            }

            if (config.getEndTimeDt() != null && now.after(config.getEndTimeDt())) {
                return;
            }

            // 异步延时处理，避免阻塞Kafka consumer线程
            Date delayedExecutionTime = new Date(System.currentTimeMillis() + 60000); // 60秒后执行
            taskScheduler.schedule(() -> processRechargeEventDelayed(rechargeEvent, mid), delayedExecutionTime);
            log.info("Scheduled delayed processing for order: {} at {}", rechargeEvent.getOrderId(), delayedExecutionTime);
        }
    }

    /**
     * 延时处理充值事件的方法，在独立线程中异步执行
     */
    @Transactional
    public void processRechargeEventDelayed(RechargeEvent rechargeEvent, String mid) {
        try {
            log.info("Processing delayed recharge event: {}", rechargeEvent.getOrderId());

            MerchantConfig config = merchantConfigs.get(mid);
            if (config == null) {
                log.warn("MerchantConfig not found for mid: {}", mid);
                return;
            }
            RechargeRecordEntity record = rechargeRecordRepository.findByOrderId(rechargeEvent.getOrderId());
            if (record != null && record.getStatus() == 1) {

                // 如何完成时间在当前时间之前1小时，就不处理

                if (record.getCompleteTime().before(new Date(System.currentTimeMillis() - 3600000))) {
                    log.info("Record timeout, not processing: {}, {}", rechargeEvent.getOrderId(), rechargeEvent.getAmount());
                    return;
                }


                log.info("Record processed: {}, {}", rechargeEvent.getOrderId(), rechargeEvent.getAmount());
                record.setStatus(5);
                rechargeRecordRepository.save(record);

                // 使用原子操作确保线程安全
                totalProcessed.incrementAndGet();
                totalAmount.addAndGet(rechargeEvent.getAmount());

                // 同步更新配置金额
                synchronized (config) {
                    config.setAmount(config.getAmount() + rechargeEvent.getAmount());
                }

                log.info("Successfully processed delayed recharge: {}, amount: {}",
                        rechargeEvent.getOrderId(), rechargeEvent.getAmount());
            } else {
                log.info("Record not found or status not eligible for processing: {}", rechargeEvent.getOrderId());
            }
        } catch (Exception e) {
            log.error("Error processing delayed recharge event: {}", rechargeEvent.getOrderId(), e);
        }
    }

    public void resetStat() {
        DateTime now = new DateTime();
        DateTime statDate = new DateTime(today);
        if (now.getDayOfMonth() != statDate.getDayOfMonth()) {
            log.info("Reset stat: {}, {}", totalProcessed.get(), totalAmount.get());
            totalProcessed.set(0);
            totalAmount.set(0);
            today = now.toDate();

            for (String mid : merchantConfigs.keySet()) {
                MerchantConfig config = merchantConfigs.get(mid);
                synchronized (config) {
                    config.setAmount(0);
                }
                if(config.getStartTimeDt() != null) {
                    config.setStartTimeDt(getDateFromTime(config.getStartTime()));
                }
                if(config.getEndTimeDt() != null) {
                    config.setEndTimeDt(getDateFromTime(config.getEndTime()));
                }
            }
        }
    }

    public void todaySummary() {
        String content = String.format("Today summary: %d, %d", totalProcessed.get(), totalAmount.get());
        log.info(content);

        resetStat();
        EmailSender sender = new EmailSender("<EMAIL>", "C6wH3Y254Y3kyyg2", "smtp.exmail.qq.com");
        String dt = new DateTime(today).toString("yyyy-MM-dd");
        sender.sendEmail("<EMAIL>", "Monitor " + dt, content);
    }

    private static Date getDateFromTime(String timeString) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
            LocalTime time = LocalTime.parse(timeString, formatter);

            DateTime dateTime = new DateTime();
            dateTime = dateTime.withTime(time.getHour(), time.getMinute(), 0, 0);

            return dateTime.toDate();
        } catch (Exception e) {
            return null;
        }
    }

    public static void main(String[] args) {
        String timeString = "14:30"; // Example input
        System.out.println(getDateFromTime(timeString));
    }
}
