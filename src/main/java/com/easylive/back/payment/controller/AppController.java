package com.easylive.back.payment.controller;

import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021/9/27
 */
@RestController
@RequestMapping(value = "/app/")
public class AppController {

    @ApiOperation(value = "充值列表信息", notes = "根据客户端传递的UA或者参数返回充值列表信息")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "充值列表信息")})
    @RequestMapping(value = "/option/list")
    public void getOptionList(@RequestParam long uid, @RequestHeader(value = HttpHeaders.USER_AGENT) String userAgent) {

    }

    @RequestMapping( value = "/order/channel")
    public void unifiedCreateOrder(@RequestParam long uid, @RequestParam long amount, @RequestParam String channel) {

    }

    @RequestMapping( value = "/order/ali/app")
    public void aliAppOrder(@RequestParam long uid, @RequestParam long amount) {

    }

    @RequestMapping( value = "/order/ali/wap")
    public void aliWapOrder(@RequestParam long uid, @RequestParam long amount) {

    }

    @RequestMapping( value = "/order/ali/pc")
    public void aliPCOrder(@RequestParam long uid, @RequestParam long amount) {

    }


    @RequestMapping( value = "/order/weixin/app")
    public void weixinAppOrder(@RequestParam long uid, @RequestParam long amount) {

    }

    @RequestMapping( value = "/order/weixin/h5")
    public void weixinH5Order(@RequestParam long uid, @RequestParam long amount) {

    }

    @RequestMapping( value = "/order/weixin/mp")
    public void weixinMPOrder(@RequestParam long uid, @RequestParam long amount) {

    }


    @RequestMapping( value = "/order/union/ali/app")
    public void unionAliAppOrder(@RequestParam long uid, @RequestParam long amount) {

    }
}
