package com.easylive.back.payment.controller;

import com.easylive.back.payment.entity.RechargeStatProjection;
import com.easylive.back.payment.service.PaymentMonitorService;
import com.easylive.common.util.DateTimeUtil;
import com.easylive.rpc.http.ResponseCommonError;
import com.easylive.rpc.http.ResponseException;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/5
 */
@RestController
@RequestMapping(value = "/admin/")
public class AdminController {

    @Autowired
    private PaymentMonitorService paymentMonitorService;

    @RequestMapping(value = "/monitor/configs")
    public String getConfigst() {
        return paymentMonitorService.getConfigs();
    }

    @RequestMapping(value = "/monitor/config/set")
    public PaymentMonitorService.MerchantConfig setAmountLimit(@RequestParam String mid,
                                                               @RequestParam(required = false) String startTime,
                                                               @RequestParam(required = false) String endTime,
                                                               @RequestParam(required = false) Long amountLimit) {
        return paymentMonitorService.setMerchantConfig(mid, amountLimit, startTime, endTime);
    }

    @RequestMapping(value = "/record/operate")
    public int statReset(@RequestParam boolean on,
                         @RequestParam(required = false) String start,
                         @RequestParam(required = false) String end,
                         @RequestParam(required = false) String sc) {
        Pair<Date, Date> dateRange = getDateRange(sc, start, end);
        return paymentMonitorService.operateRecord(on, dateRange.getLeft(), dateRange.getRight());
    }

    private Pair<Date, Date> getDateRange(String sc, String start, String end) {
        Date startDt = null;
        Date endDt = null;

        if (start != null) {
            startDt = DateTimeUtil.getDayFormat().parseDateTime(start).toDate();
        }

        if (end != null) {
            endDt = DateTimeUtil.getDayFormat().parseDateTime(end).toDate();
        }

        if ("lastweek".equals(sc)) {
            startDt = DateTimeUtil.startOfLastWeek();
            endDt = DateTimeUtil.startOfWeek();
        } else if ("lastmonth".equals(sc)) {
            startDt = DateTimeUtil.startOfLastMonth();
            endDt = DateTimeUtil.startOfMonth();
        } else if ("yesterday".equals(sc)) {
            startDt = DateTimeUtil.yesterday();
            endDt = DateTimeUtil.today();
        } else if ("today".equals(sc)) {
            startDt = DateTimeUtil.today();
            endDt = DateTimeUtil.now();
        } else if ("week".equals(sc)) {
            startDt = DateTimeUtil.startOfWeek();
            endDt = DateTimeUtil.now();
        } else if ("month".equals(sc)) {
            startDt = DateTimeUtil.startOfMonth();
            endDt = DateTimeUtil.now();
        }


        if (startDt == null || endDt == null) {
            throw new ResponseException(ResponseCommonError.E_PARAM, "date not specified");
        }

        return Pair.of(startDt, endDt);
    }

    @RequestMapping(value = "/record/stat")
    public RechargeStatProjection statRecord(@RequestParam Boolean on,
                                             @RequestParam(required = false) String start,
                                             @RequestParam(required = false) String end,
                                             @RequestParam(required = false) String sc) {
        Pair<Date, Date> dateRange = getDateRange(sc, start, end);
        return paymentMonitorService.statRecord(on, dateRange.getLeft(), dateRange.getRight());
    }

}
