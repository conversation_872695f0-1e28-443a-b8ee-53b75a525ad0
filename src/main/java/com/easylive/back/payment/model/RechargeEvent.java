package com.easylive.back.payment.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/2/7
 */
@Data
public class RechargeEvent {

    private long uid;
    private long amount;
    private long ecoin;
    private int platform;
    private String orderId;
    private String platformOrderId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date commitTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date completeTime;
    private String currency;
    private String merchantCurrency;
    private String merchantId;
    private String tid;
    private int appId;
    private int userAppId = 1;
    private int userAppSourceId = 1;
    private int clientPlatform;
}