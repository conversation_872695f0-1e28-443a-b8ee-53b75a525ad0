package com.easylive.back.payment.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @date 2018/11/25
 */
@PropertySource(value = "classpath:version.properties", ignoreResourceNotFound = true)
@Configuration
@Component
public class VersionConfig {

    private static final Logger logger = LoggerFactory.getLogger(VersionConfig.class);
    @Value("${version:unknown}")
    String version;

    @Value("${commit:unknown}")
    String commit;

    @Value("${date:unknown}")
    String date;

    @Value("${build:unknown}")
    String build;

    public String getVersion() {
        return version;
    }

    public String getBuild() {
        return build;
    }

    public String getDate() {
        return date;
    }

    public String getCommit() {
        return commit;
    }

    public String fullString() {
        return String.format("%s-%s, Build %s-%s", version, commit, build, date);
    }


    @PostConstruct
    public void init() {
        logger.info("Application Version: {}", fullString());
    }
}
