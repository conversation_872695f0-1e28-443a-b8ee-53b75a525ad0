package com.easylive.back.payment.config;

import com.easylive.common.event.EventConsumer;
import com.easylive.common.event.EventProducer;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 */

@Slf4j
@Configuration
public class KafkaConfig {

    @Value("${spring.kafka.bootstrap-servers}")
    private String bootStrapServer;

    @Value("${spring.kafka.consumer.group-id}")
    protected String groupId;

    @Autowired
    @Qualifier("paymentThreadPool")
    private Executor taskExecutor;

    @Value("${kafka.consumer.switch:true}")
    @Getter
    protected boolean eventConsumeSwitch;

    @Bean
    public EventProducer eventProducer() {
        Map<String, Object> configProps = new HashMap<>();
        configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootStrapServer);
        EventProducer eventProducer = new EventProducer(configProps);
        eventProducer.start(taskExecutor);
        return eventProducer;
    }


    @Bean
    public EventConsumer eventConsumer() {
        if (!eventConsumeSwitch){
            return null;
        }
        Map<String, Object> configProps = new HashMap<>();
        configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootStrapServer);
        configProps.put(ConsumerConfig.GROUP_ID_CONFIG, groupId);
        return new EventConsumer(configProps);
    }

    @Bean
    DisposableBean eventStopBean() {
        return new DisposableBean() {
            @Autowired(required = false)
            private EventConsumer eventConsumer;

            @Autowired
            private EventProducer eventProducer;

            @Override
            public void destroy() throws Exception {
                eventProducer.stop();
                if (eventConsumeSwitch){
                    eventConsumer.stop();
                }
            }
        };
    }
}
