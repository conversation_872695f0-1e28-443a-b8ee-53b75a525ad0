package com.easylive.back.payment.config.feign;

import feign.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2022/6/7 10:22
 */
@Configuration
public class FeignConfig {

    @Value("${user.feign.logging.level:NONE}")
    private String loggingLevel;

    @Bean
    Logger.Level getLoggerLevel() {
        return Logger.Level.valueOf(loggingLevel.toUpperCase());
    }

}
