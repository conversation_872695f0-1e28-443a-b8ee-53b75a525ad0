package com.easylive.back.payment.config.feign;

import com.easylive.back.payment.common.Constants;
import com.easylive.rpc.http.feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.context.annotation.Configuration;

/**
 * 设置Feign的Header
 *
 * <AUTHOR>
 * @date 2022/6/7 11:20
 */
@Configuration
public class FeignRequestInterceptor extends RequestInterceptor {

    public FeignRequestInterceptor() {
        super(Constants.USER_AGENT);
    }

    @Override
    public void apply(RequestTemplate requestTemplate) {
        super.apply(requestTemplate);
    }
}
