package com.easylive.back.payment.common;

import com.easylive.rpc.http.GlobalExceptionHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.web.bind.annotation.ControllerAdvice;

/**
 * 这里可以覆盖基类的处理方式，但是最好继承ResponseErrorHandler把全部的处理继承过来
 */
@ControllerAdvice
@Order(value = Ordered.HIGHEST_PRECEDENCE)
public class SpringExceptionHandler extends GlobalExceptionHandler {
    private static final Logger logger = LoggerFactory.getLogger(SpringExceptionHandler.class);

    /**
     * 覆盖common里面的Exception处理
     */
    /*
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ResponseModel> handleException(Exception exception) {
        logger.error("My Unhandled exception caught", exception);
        ResponseModel model = new ResponseModel(MyError.E_MY_BIZ_ERROR, "Internal Server Error");
        return new ResponseEntity<>(model, HttpStatus.INTERNAL_SERVER_ERROR);
    }
    */
}
