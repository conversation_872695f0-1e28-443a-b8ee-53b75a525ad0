package com.easylive.back.payment.common;

import com.easylive.rpc.http.ResponseCommonError;
import com.easylive.rpc.http.ResponseException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.MethodParameter;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.support.WebArgumentResolver;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import javax.servlet.http.HttpServletRequest;

/**
 * 处理注解RequestAttribute
 */
public class RequestAttributeResolver implements HandlerMethodArgumentResolver {

    private static final Logger logger = LoggerFactory.getLogger(RequestAttributeResolver.class);

    public Object resolveArgument(MethodParameter methodParameter,
                                  NativeWebRequest nativeWebRequest) {

        RequestAttribute requestAttributeAnnotation = methodParameter.getParameterAnnotation(RequestAttribute.class);

        if (requestAttributeAnnotation != null) {
            HttpServletRequest request = (HttpServletRequest) nativeWebRequest.getNativeRequest();
            return request.getAttribute(requestAttributeAnnotation.value());
        }

        return WebArgumentResolver.UNRESOLVED;
    }

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        RequestAttribute requestAttributeAnnotation = parameter.getParameterAnnotation(RequestAttribute.class);
        return requestAttributeAnnotation != null;
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer, NativeWebRequest webRequest, WebDataBinderFactory binderFactory) {
        RequestAttribute requestAttributeAnnotation = parameter.getParameterAnnotation(RequestAttribute.class);
        HttpServletRequest request = (HttpServletRequest) webRequest.getNativeRequest();
        String attrName = requestAttributeAnnotation.value();
        if (StringUtils.isEmpty(attrName)) {
            PublicAuthCheck publicAuthCheck = parameter.getMethodAnnotation(PublicAuthCheck.class);
            if (publicAuthCheck == null) {
                logger.error("PublicAuthCheck annotation must be defined for this method");
                throw new ResponseException(ResponseCommonError.E_SERVER);
            }

            attrName = "publicServiceClient";
        }

        return request.getAttribute(attrName);
    }
}
