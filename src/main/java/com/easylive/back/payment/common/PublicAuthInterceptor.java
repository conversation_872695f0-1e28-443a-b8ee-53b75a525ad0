package com.easylive.back.payment.common;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 使用Interceptor来处理生成RequestAttribute注解的对象
 */
public class PublicAuthInterceptor extends HandlerInterceptorAdapter {


    private static Logger logger = LoggerFactory.getLogger(PublicAuthInterceptor.class);

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        if (handler instanceof HandlerMethod) {
            HandlerMethod method = (HandlerMethod) handler;
            PublicAuthCheck check = method.getMethod().getAnnotation(PublicAuthCheck.class);
            if (check == null) {
                return true;
            }
        } else {
            return super.preHandle(request, response, handler);
        }

        //这里需要替换为相应的对象
        request.setAttribute("publicServiceClient", null);

        return super.preHandle(request, response, handler);
    }

    private String getIPAddress(HttpServletRequest request) {
        String ip = request.getHeader("X-Real-IP");
        if (ip != null)
            return ip;

        ip = request.getHeader("X-Forwarded-For");
        if (ip != null) {
            return ip.split(",")[0];
        }

        return request.getRemoteAddr();
    }
}
