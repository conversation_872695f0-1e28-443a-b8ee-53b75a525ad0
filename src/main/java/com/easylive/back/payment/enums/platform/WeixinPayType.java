package com.easylive.back.payment.enums.platform;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/12/16
 */
public enum WeixinPayType {
    APP(0, "APP"),
    MP(1, "JSAPI"),
    CODE(2, "NATIVE"),
    H5(3, "MWEB"),
    MINIAPP(4, "JSAPI");

    private static final Map<Integer, WeixinPayType> MAP = new HashMap<>();


    static {
        for (WeixinPayType weixinPayType : WeixinPayType.values()) {
            MAP.put(weixinPayType.getValue(), weixinPayType);
        }
    }

    private final String tradeType;
    private final int value;

    WeixinPayType(int value, final String tradeType) {
        this.value = value;
        this.tradeType = tradeType;
    }

    public static WeixinPayType fromValue(int value) {
        return MAP.get(value);
    }

    public static List<Integer> fromTradeType(String tradeType) {
        List<Integer> wxPayTypeList = new ArrayList<>();
        for (WeixinPayType type : WeixinPayType.values()) {
            if (type.getTradeType().equals(tradeType))
                wxPayTypeList.add(type.ordinal());
        }
        return wxPayTypeList;
    }

    public String getTradeType() {
        return tradeType;
    }

    public int getValue() {
        return value;
    }
}
