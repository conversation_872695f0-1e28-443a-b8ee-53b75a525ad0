package com.easylive.back.payment.enums;

import org.joda.time.DateTime;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 配置项数据类型
 */
public enum ConfigDataType {

    //字符串
    STRING(0),

    //整型
    INTEGER(1),

    //小数
    DECIMAL(2),

    //时间日期
    DATETIME(3);

    private static final Map<Integer, ConfigDataType> MAPS = new HashMap<>();
    private static final Map<Integer, Class> CLASS_MAP = new HashMap<>();

    static {
        MAPS.put(STRING.value, STRING);
        MAPS.put(INTEGER.value, INTEGER);
        MAPS.put(DECIMAL.value, DECIMAL);
        MAPS.put(DATETIME.value, DATETIME);

        CLASS_MAP.put(STRING.value, String.class);
        CLASS_MAP.put(INTEGER.value, BigInteger.class);
        CLASS_MAP.put(DECIMAL.value, BigDecimal.class);
        CLASS_MAP.put(DATETIME.value, Date.class);

    }

    private final int value;


    ConfigDataType(final int newValue) {
        value = newValue;
    }

    public static ConfigDataType fromValue(int value) {
        ConfigDataType configDataType = MAPS.get(value);
        return configDataType == null ? STRING : configDataType;
    }

    public static Class getDataTypeClass(int value) {
        Class classType = CLASS_MAP.get(value);
        return classType == null ? String.class : classType;
    }

    public static Object convert(String value, int type) {
        return convert(value, fromValue(type));
    }

    public static Object convert(String value, ConfigDataType type) {
        if (value != null) {
            if (type == INTEGER) {
                return new BigInteger(value);
            } else if (type == DECIMAL) {
                return new BigDecimal(value);
            } else if (type == DATETIME) {
                return DateTime.parse(value);
            }
        }
        return value;
    }

    public int getValue() {
        return value;
    }

    public Class getValueClass() {
        return getDataTypeClass(value);
    }
}
