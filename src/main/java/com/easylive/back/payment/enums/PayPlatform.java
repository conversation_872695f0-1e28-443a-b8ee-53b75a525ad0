package com.easylive.back.payment.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 支付平台，指的是集成使用的支付平台，即用了哪一家的支付SDK.客户端需要知道这个配置。来使用对应的SDK
 *
 * <AUTHOR>
 * @date 2021/11/23
 */
public enum PayPlatform {
    SYSTEM(0),
    ALI(1),
    WEIXIN(2),
    APPLE(3),
    GOOGLE(4),
    PAYPAL(5),
    UNION(6),
    BAIDU(7),
    OPPO(8),
    GASH(9);


    private static final Map<Integer, PayPlatform> MAP = new HashMap<>();

    static {
        for (PayPlatform payPlatform : PayPlatform.values()) {
            MAP.put(payPlatform.getValue(), payPlatform);
        }
    }

    private final int value;

    PayPlatform(final int newValue) {
        value = newValue;
    }

    public static PayPlatform fromValue(int value) {
        return MAP.get(value);
    }

    public int getValue() {
        return value;
    }
}
