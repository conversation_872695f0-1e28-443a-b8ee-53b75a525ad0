package com.easylive.back.payment.enums.platform;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/12/16
 */
public enum AliPayType {
    APP(0, "APP"),
    WAP(1, "WAP"),
    PC(2, "PC");

    private static final Map<Integer, AliPayType> MAP = new HashMap<>();


    static {
        for (AliPayType aliPayType : AliPayType.values()) {
            MAP.put(aliPayType.getValue(), aliPayType);
        }
    }

    private final String tradeType;
    private final int value;

    AliPayType(int value, final String tradeType) {
        this.value = value;
        this.tradeType = tradeType;
    }

    public static AliPayType fromValue(int value) {
        return MAP.get(value);
    }

    public static List<Integer> fromTradeType(String tradeType) {
        List<Integer> wxPayTypeList = new ArrayList<>();
        for (AliPayType type : AliPayType.values()) {
            if (type.getTradeType().equals(tradeType))
                wxPayTypeList.add(type.ordinal());
        }
        return wxPayTypeList;
    }

    public String getTradeType() {
        return tradeType;
    }

    public int getValue() {
        return value;
    }
}
