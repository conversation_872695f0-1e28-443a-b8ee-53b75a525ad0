package com.easylive.back.payment.util;

import lombok.extern.slf4j.Slf4j;

import javax.mail.*;
import javax.mail.internet.*;
import java.util.Properties;
/**
 * <AUTHOR>
 * @date 2025/2/7
 */
@Slf4j
public class EmailSender {
    private String username; // 发件人邮箱账号
    private String password; // 发件人邮箱密码
    private String host; // SMTP服务器地址

    public EmailSender(String username, String password, String host) {
        this.username = username;
        this.password = password;
        this.host = host;
    }

    public void sendEmail(String to, String subject, String content)  {
        log.info("Sending email to: {}", to);
        // 配置邮件属性
        Properties properties = new Properties();
        properties.put("mail.smtp.auth", "true"); // 开启认证
        //properties.put("mail.smtp.starttls.enable", "true"); // 开启TLS加密
        properties.put("mail.smtp.socketFactory.port", "465");
        properties.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
        properties.put("mail.smtp.host", host); // 设置SMTP服务器地址
        properties.put("mail.smtp.port", "465"); // 设置SMTP端口号

        // 创建Session对象
        Session session = Session.getInstance(properties, new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(username, password);
            }
        });

        try {
            // 创建MimeMessage对象
            MimeMessage message = new MimeMessage(session);
            message.setFrom(new InternetAddress(username)); // 设置发件人
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(to)); // 设置收件人
            message.setSubject(subject); // 设置邮件主题
            message.setText(content); // 设置邮件内容

            // 发送邮件
            Transport.send(message);
            log.info("Delivered");
        } catch (MessagingException e) {
            log.info("Error sending email",e);
        }

    }

    public static void main(String[] args) throws MessagingException {
        EmailSender sender = new EmailSender("<EMAIL>", "C6wH3Y254Y3kyyg2", "smtp.exmail.qq.com");
        sender.sendEmail("<EMAIL>", "Test Subject", "This is a test email.");
    }
}
