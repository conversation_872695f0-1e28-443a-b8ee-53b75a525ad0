package com.easylive.back.payment.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Entity
@Data
@Table(name = "t_recharge_record")
public class RechargeRecordEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonIgnore
    private Long id;
    private long uid;
    private int status;
    private long rmb;

    private String currency;
    private Long amount;

    private long ecoin;
    private String tid;


    @Column(name = "mid")
    private String merchantId;

    @Column(name = "mcurrency")
    private String merchantCurrency;

    @Column(name = "commit_time")
    private Date commitTime;

    @Column(name = "complete_time")
    private Date completeTime;

    @Column(name = "confirm_status")
    private int confirmStatus;

    @Column(name = "order_id")
    private String orderId;

    @Column(name = "client_ip")
    private String clientIp;

    @Column(name = "platform")
    private int platform;

    @Column(name = "real_platform")
    private Integer realPlatform;

    @Column(name = "clientplatform")
    private int clientPlatform;
    // 支付平台的订单号

    @Column(name = "pforderid")
    private String platformOrderId;

    @Column(name = "app_id")
    private int appId;

}
