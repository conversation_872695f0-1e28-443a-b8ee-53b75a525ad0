package com.easylive.back.payment;

import com.easylive.common.util.SystemUtil;
import io.undertow.Undertow;
import io.undertow.UndertowOptions;
import io.undertow.server.ConnectorStatistics;
import io.undertow.server.HandlerWrapper;
import io.undertow.server.HttpHandler;
import io.undertow.server.handlers.GracefulShutdownHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.embedded.undertow.UndertowServletWebServer;
import org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory;
import org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ApplicationListener;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.DispatcherServlet;

import java.lang.reflect.Field;
import java.util.List;

@SpringBootApplication
//@EnableDiscoveryClient
//@EnableFeignClients(basePackages = {"com.easylive.mgs"})
@Slf4j
public class Application {

    @Value("${spring.profiles.active:Unknown}")
    private String activeProfile;
    public static void main(String[] args) {
        ConfigurableApplicationContext context = SpringApplication.run(Application.class, args);
        context.getBean(DispatcherServlet.class).setThrowExceptionIfNoHandlerFound(true);
    }



    @Component
    public class GracefulShutdown implements ApplicationListener<ContextClosedEvent> {

        @Autowired
        private GracefulShutdownWrapper gracefulShutdownWrapper;

        @Autowired
        private ServletWebServerApplicationContext context;

        @Override
        public void onApplicationEvent(ContextClosedEvent contextClosedEvent) {
            gracefulShutdownWrapper.getGracefulShutdownHandler().shutdown();
            try {
                UndertowServletWebServer webServer = (UndertowServletWebServer) context.getWebServer();
                if (webServer == null)
                    return;

                Field field = webServer.getClass().getDeclaredField("undertow");
                field.setAccessible(true);
                Undertow undertow = (Undertow) field.get(webServer);
                List<Undertow.ListenerInfo> listenerInfo = undertow.getListenerInfo();
                Undertow.ListenerInfo listener = listenerInfo.get(0);
                ConnectorStatistics connectorStatistics = listener.getConnectorStatistics();
                long connections;
                long maxWaitTime = 60000;
                long start = System.currentTimeMillis();
                while (true) {
                    connections = connectorStatistics.getActiveConnections();
                    if (connections <= 0) {
                        log.info("No active connections found. Now shutdown safely.");
                        break;
                    }

                    long left = maxWaitTime - (System.currentTimeMillis() - start);
                    if (left < 0 || "dev".equals(activeProfile)) {
                        log.info("Max waiting time reached. All connections will be terminated forcibly.");
                        break;
                    }

                    log.info("waiting for all connections to complete, {} connections, {} ms remaining", connections, left);
                    SystemUtil.sleep(1000);
                }

            } catch (Exception e) {                // Application Shutdown
                log.error("Error when handling gracefully shutdown", e);
            }
        }
    }

    @Component
    public class GracefulShutdownWrapper implements HandlerWrapper {
        private GracefulShutdownHandler gracefulShutdownHandler;

        @Override
        public HttpHandler wrap(HttpHandler handler) {
            if (gracefulShutdownHandler == null) {
                this.gracefulShutdownHandler = new GracefulShutdownHandler(handler);
            }
            return gracefulShutdownHandler;
        }

        public GracefulShutdownHandler getGracefulShutdownHandler() {
            return gracefulShutdownHandler;
        }
    }


    @Component
    public class UndertowExtraConfiguration {
        @Autowired
        private GracefulShutdownWrapper gracefulShutdownWrapper;

        @Bean
        public UndertowServletWebServerFactory servletWebServerFactory() {
            UndertowServletWebServerFactory factory = new UndertowServletWebServerFactory();
            factory.addDeploymentInfoCustomizers(deploymentInfo -> deploymentInfo.addOuterHandlerChainWrapper(gracefulShutdownWrapper));
            factory.addBuilderCustomizers(builder -> builder.setServerOption(UndertowOptions.ENABLE_STATISTICS, true));
            return factory;
        }

    }
}

