server.port = 8080
spring.profiles.active=dev
server.shutdown=graceful
spring.lifecycle.timeout-per-shutdown-phase=30s

#spring boot admin
#spring.boot.admin.client.url=http://localhost:8080
#management.endpoints.web.exposure.include=*

spring.application.name=payment
spring.cloud.nacos.discovery.server-addr=el-dev002:8848
management.endpoints.web.exposure.include=*
management.metrics.tags.application=${spring.application.name}
management.endpoints.web.base-path=/actuator


# Database

spring.datasource.jdbc-url=*************************************************************************************************************************************
spring.datasource.username=yizhibo
spring.datasource.password=Yizhibo@2015
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.maximum-pool-size=4
spring.datasource.minimum-idle=1

spring.datasource.secondary.jdbc-url=*************************************************************************************************************************************
spring.datasource.secondary.username=username
spring.datasource.secondary.password=password
spring.datasource.secondary.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.secondary.maximum-pool-size=4
spring.datasource.secondary.minimum-idle=1


# Redis
spring.redis.host=dev001
spring.redis.port=6379
spring.redis.password=
spring.redis.lettuce.pool.min-idle=1
spring.redis.lettuce.pool.max-active=1

#kafka
spring.kafka.bootstrap-servers=dev001:9092
spring.kafka.consumer.group-id=payment-service
spring.kafka.consumer.auto-offset-reset=latest
