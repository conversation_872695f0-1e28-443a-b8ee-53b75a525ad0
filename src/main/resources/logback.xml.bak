<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yy-MM-dd HH:mm:ss.SSS} %5level [%-16.16thread] %-40logger{40}:%-3L - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="file" class="ch.qos.logback.core.FileAppender">
        <file>error.log</file>
        <append>true</append>
        <encoder>
            <pattern>%d{yy-MM-dd HH:mm:ss.SSS} %5level [%-16.16thread] %-40.40logger{40}:%-3L - %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
    </appender>

    <!-- project default level -->
    <logger name="com.easylive" level="DEGUG"/>
    <logger name="org.springframework" level="INFO"/>
    <logger name="feign" level="DEBUG"/>

    <root level="INFO">
        <appender-ref ref="console"/>
        <appender-ref ref="file"/>
    </root>
</configuration>