server.port = 8898
server.shutdown=graceful
spring.lifecycle.timeout-per-shutdown-phase=30s


#spring boot admin
#spring.boot.admin.client.url=http://localhost:8080

spring.application.name=payment
spring.cloud.nacos.discovery.server-addr=nacos1:8848,nacos2:8848,nacos3:8848
ribbon.ServerListRefreshInterval=5000
management.endpoints.web.exposure.include=*
management.metrics.tags.application=${spring.application.name}
management.endpoints.web.base-path=/actuator


# Database

spring.datasource.jdbc-url=*************************************************************************************************************************************************
spring.datasource.username=yizhibo
spring.datasource.password=Yizhibo2015
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.maximum-pool-size=4
spring.datasource.minimum-idle=1

spring.datasource.secondary.jdbc-url=*************************************************************************************************************************************************
spring.datasource.secondary.username=yizhibo
spring.datasource.secondary.password=Yizhibo2015
spring.datasource.secondary.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.secondary.maximum-pool-size=16
spring.datasource.secondary.minimum-idle=1

spring.main.allow-bean-definition-overriding=true

# Redis
spring.redis.host=main.redis.yizhibo.tv
spring.redis.port=6379
spring.redis.password=Yizhibo2015
spring.redis.lettuce.pool.min-idle=4
spring.redis.lettuce.pool.max-active=16

#kafka
spring.kafka.bootstrap-servers=ke4:9092,ke5:9092,ke6:9092
spring.kafka.consumer.group-id=payment-service
spring.kafka.consumer.auto-offset-reset=latest