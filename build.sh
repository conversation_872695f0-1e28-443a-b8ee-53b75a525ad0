echo "Backup config files"
BASE=src/main/resources
cp -f $BASE/application.properties $BASE/application.properties.bak
cp -f $BASE/logback.xml $BASE/logback.xml.bak

echo "Create online config files"
cp -f $BASE/application.properties.template.online $BASE/application.properties
cp -f $BASE/logback.xml.template.online $BASE/logback.xml


echo "Building..."
gradle build -x test

echo "Restore config files"
cp -f $BASE/application.properties.bak $BASE/application.properties
cp -f $BASE/logback.xml.bak $BASE/logback.xml

